import React from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { useCartStore } from '@features/cart/cart-state'
import { CartTotalCode } from '@lib/_generated/graphql_sdk'
import { BNPPayment } from '@components/jet-leasing/BNPPayment/BNPPayment'
import { PaymentSchema } from '@features/checkout/components/forms/payment/payment.schema'

interface BNPPaymentWrapperProps {
  className?: string
}

export const BNPPaymentWrapper: React.FC<BNPPaymentWrapperProps> = ({ className = '' }) => {
  const cartTotals = useCartStore((state) => state.totals)
  const cartToken = useCartStore((state) => state.token)
  const form = useFormContext<PaymentSchema>()

  // Watch for changes in simplified BNP payment fields
  const bnpSelectedVariantId = useWatch({
    control: form.control,
    name: 'bnpSelectedVariantId'
  })

  const bnpEgn = useWatch({
    control: form.control,
    name: 'bnpEgn'
  })

  const bnpPrivacyAgreed = useWatch({
    control: form.control,
    name: 'bnpPrivacyAgreed'
  })

  if (!cartToken) {
    return (
      <div className="col-span-2">
        <div className="text-center py-8">
          <p className="text-gray-600">Зареждане на количката...</p>
        </div>
      </div>
    )
  }

  const handleVariantSelect = (variantId: string) => {
    console.log('BNP Variant Selected:', variantId)
    form.setValue('bnpSelectedVariantId', variantId, { shouldValidate: true })
  }

  const handleEgnChange = (egn: string) => {
    form.setValue('bnpEgn', egn, { shouldValidate: true })
  }

  const handlePrivacyChange = (agreed: boolean) => {
    form.setValue('bnpPrivacyAgreed', agreed, { shouldValidate: true })
  }

  const handleError = (error: string) => {
    console.error('BNP Payment Error:', error)

    // Set form error
    form.setError('bnpSelectedVariantId', {
      type: 'manual',
      message: error
    })
  }

  return (
    <div className={`col-span-2 ${className}`}>
      <BNPPayment
        totalPrice={cartTotals.find((total) => total.code === CartTotalCode.GrantTotal)?.amount?.value || 0}
        cartToken={cartToken}
        onVariantSelect={handleVariantSelect}
        onEgnChange={handleEgnChange}
        onPrivacyChange={handlePrivacyChange}
        onError={handleError}
        className="mt-4"
        // Pass current form values
        selectedVariantId={bnpSelectedVariantId}
        egn={bnpEgn}
        privacyAgreed={bnpPrivacyAgreed}
      />
    </div>
  )
}
