'use client'
import React, { useState } from 'react'
import {
  BNPPaymentProps,
  BNPPaymentState,
  BNPPricingVariant,
  BNPCustomerData,
  BNPPaymentInput,
} from './types'
import { GraphQLBackend } from '@lib/api/graphql'
import { ParibaError } from '@components/jet-leasing/BNPPayment/components/ParibaError'
import { ParibaStepVariants } from './components/ParibaStepVariants'
import { BNPDownPayment } from '@components/jet-leasing/BNPPayment/BNPDownPayment'

export const BNPPayment: React.FC<BNPPaymentProps> = ({
  cartToken,
  totalPrice,
  onPaymentSelect,
  onError,
  className = '',
}) => {
  const [state, setState] = useState<BNPPaymentState>({
    loading: false,
    error: null,
    schemes: [],
    selectedScheme: null,
    variantGroups: [],
    selectedVariant: null,
    loanCalculation: null,
    customerData: null,
    downPayment: null,
    goodTypeIds: '353', // Static fallback good type ID
    privacyAgreed: false,
  })

  const updateState = (updates: Partial<BNPPaymentState>) => {
    setState((prev) => ({ ...prev, ...updates }))
  }

  const handleVariantSelect = async (variant: BNPPricingVariant) => {
    updateState({ selectedVariant: variant })
  }

  const handleDownPaymentChange = (downPayment: number) => {
    updateState({ downPayment })
  }

  const handleCustomerFormSubmit = async (customerData: BNPCustomerData) => {
    if (!state.selectedVariant || !state.loanCalculation) {
      onError?.('Моля, изберете схема на плащане')
      return
    }
    updateState({ loading: true, customerData })
    try {
      const paymentData: BNPPaymentInput = {
        downPayment: state.downPayment || 0,
        pricingVariantId: parseInt(state.selectedVariant.id),
        customerData,
      }

      // Save BNP payment data to cart using GraphQL mutation
      const response = await GraphQLBackend.CartSaveBNPPayment({
        cartToken,
        paymentData,
      })

      if (response.cartSaveBNPPayment) {
        onPaymentSelect?.(paymentData)
      } else {
        throw new Error('Неуспешно запазване на данните за плащане')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Грешка при запазване на данните'
      updateState({ error: errorMessage })
      onError?.(errorMessage)

      // Log detailed error for debugging
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ BNP CartSaveBNPPayment error:', {
          error: err,
          customerData,
          selectedVariant: state.selectedVariant,
        })
      }
    } finally {
      updateState({ loading: false })
    }
  }

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      {state.error && <ParibaError error={state.error} />}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-gray-900 text-center">Изчислете вашите вноски</h3>
        <BNPDownPayment
          downPayment={state.downPayment}
          onDownPaymentChangeAction={handleDownPaymentChange}
          totalPrice={totalPrice}
        />
        <ParibaStepVariants
          downPayment={state.downPayment}
          loading={state.loading}
          onLoanCalculation={(loanCalculation) => updateState({ loanCalculation })}
          onSelectVariant={handleVariantSelect}
          handleCustomerFormSubmit={handleCustomerFormSubmit}
        />
      </div>
    </div>
  )
}
