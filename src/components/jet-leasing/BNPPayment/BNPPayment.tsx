'use client'
import React, { useState } from 'react'
import {
  BNPPaymentProps,
  BNPPaymentState,
  BNPPricingVariant,
  BNPCustomerData,
  BNPPaymentInput,
} from './types'
import { GraphQLBackend } from '@lib/api/graphql'
import { ParibaError } from '@components/jet-leasing/BNPPayment/components/ParibaError'
import { ParibaStepVariants } from './components/ParibaStepVariants'
import { BNPDownPayment } from '@components/jet-leasing/BNPPayment/BNPDownPayment'

export const BNPPayment: React.FC<BNPPaymentProps> = ({
  cartToken,
  totalPrice,
  onVariantSelect,
  onEgnChange,
  onError,
  className = '',
  selectedVariantId,
  egn,
}) => {
  const [state, setState] = useState<BNPPaymentState>({
    loading: false,
    error: null,
    schemes: [],
    selectedScheme: null,
    variantGroups: [],
    selectedVariant: null,
    loanCalculation: null,
    customerData: null, // No longer needed
    downPayment: null,
    goodTypeIds: '353', // Static fallback good type ID
    privacyAgreed: false,
  })

  const updateState = (updates: Partial<BNPPaymentState>) => {
    setState((prev) => ({ ...prev, ...updates }))
  }

  const handleVariantSelect = async (variant: BNPPricingVariant) => {
    updateState({ selectedVariant: variant })
    onVariantSelect?.(variant.id)
  }

  const handleDownPaymentChange = (downPayment: number) => {
    updateState({ downPayment })
  }

  const handleEgnChange = (newEgn: string) => {
    onEgnChange?.(newEgn)
  }

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      {state.error && <ParibaError error={state.error} />}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-gray-900 text-center">Изчислете вашите вноски</h3>
        <BNPDownPayment
          downPayment={state.downPayment}
          onDownPaymentChangeAction={handleDownPaymentChange}
          totalPrice={totalPrice}
        />
        <ParibaStepVariants
          downPayment={state.downPayment}
          loading={state.loading}
          onLoanCalculation={(loanCalculation) => updateState({ loanCalculation })}
          onSelectVariant={handleVariantSelect}
          selectedVariantId={selectedVariantId}
        />

        {/* Simplified form with only EGN and Privacy Agreement */}
        <div className="space-y-4 mt-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Допълнителна информация</h4>

            {/* EGN Field */}
            <div className="mb-4">
              <label htmlFor="bnp-egn" className="block text-sm font-medium text-gray-700 mb-1">
                ЕГН <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="bnp-egn"
                name="bnp-egn"
                value={egn || ''}
                onChange={(e) => handleEgnChange(e.target.value)}
                placeholder="Въведете вашия ЕГН"
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>


          </div>
        </div>
      </div>
    </div>
  )
}
