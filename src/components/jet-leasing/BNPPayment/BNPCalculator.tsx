'use client'
import React, { useState, useEffect, useCallback, useRef } from 'react'
import { BNPCalculatorProps, BNPPricingVariant, BNPLoanCalculation, BNPVariantGroup } from './types'
import { BNPVariantSelector } from './BNPVariantSelector'
import { formatPrice, formatCurrencyInput, parseCurrencyInput, handleBNPGoodTypeError } from './utils'
import { GraphQLBackend } from '@lib/api/graphql'
import { ParibaLoanDetails } from '@components/jet-leasing/BNPPayment/components/ParibaLoanDetails'
import Cookies from 'universal-cookie'
const cookie = new Cookies()

export const BNPCalculator: React.FC<BNPCalculatorProps> = ({
  sku,
  downPayment: initialDownPayment,
  quantity,
  onVariantSelect,
  onCalculationComplete,
  onDownPaymentChange,
  className = '',
}) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [variantGroups, setVariantGroups] = useState<BNPVariantGroup[]>([])
  const [selectedVariant, setSelectedVariant] = useState<BNPPricingVariant | null>(null)
  const [loanCalculation, setLoanCalculation] = useState<BNPLoanCalculation | null>(null)
  const [calculationLoading, setCalculationLoading] = useState(false)
  const [downPayment, setDownPayment] = useState<number>(initialDownPayment || 0)
  const [downPaymentInput, setDownPaymentInput] = useState<string>(initialDownPayment?.toString() || '')
  const [downPaymentError, setDownPaymentError] = useState<string | null>(null)
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Calculate product price for validation
  const calculateProductPrice = useCallback((): number => {
    // This should be calculated based on actual product data
    // For now, using a reasonable estimate
    return 2500.0 * quantity
  }, [quantity])

  // Validate down payment amount
  const validateDownPayment = (amount: number): string | null => {
    const productPrice = calculateProductPrice()
    const minDownPayment = productPrice * 0.1 // 10% minimum
    const maxDownPayment = productPrice * 0.8 // 80% maximum

    if (amount < 0) {
      return 'Първоначалната вноска не може да бъде отрицателна'
    }
    if (amount < minDownPayment) {
      return `Минималната първоначална вноска е ${formatPrice(minDownPayment)}`
    }
    if (amount > maxDownPayment) {
      return `Максималната първоначална вноска е ${formatPrice(maxDownPayment)}`
    }
    return null
  }

  // Format down payment input for display
  const formatDownPaymentInput = (value: string): string => {
    const number = parseCurrencyInput(value)
    if (number === 0) return ''
    return number.toFixed(2).replace('.', ',')
  }

  const loadPricingVariants = async () => {
    setLoading(true)
    setError(null)

    try {
      // Use the generated GraphQL function
      const response = await GraphQLBackend.GetCreditCalculatorBNPParibas({
        sku,
        downPayment,
        qty: quantity,
      })

      if (response.getCreditCalculatorBNPParibas) {
        // Transform response to variant groups with promo detection
        const groups: BNPVariantGroup[] = response.getCreditCalculatorBNPParibas.map((group) => {
          const enhancedVariants = group.variants.map((variant) => ({
            ...variant,
            isPromo: parseFloat(variant.apr) === 0,
            processingFeeAmount: variant.processingFeeAmount || '50.00',
          }))

          return {
            schemeId: group.schemeId,
            schemeName: enhancedVariants[0]?.pricingSchemeName || `Схема ${group.schemeId}`,
            variants: enhancedVariants,
          }
        })

        setVariantGroups(groups)

        // Auto-select first variant from first group
        if (groups.length > 0 && groups[0].variants.length > 0) {
          setSelectedVariant(groups[0].variants[0])
        }
      } else {
        // Fallback to empty variant groups if no data
        setVariantGroups([])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Грешка при зареждане на варианти')
      console.error('Error loading pricing variants:', err)

      // Fallback to empty variant groups on error
      setVariantGroups([])
    } finally {
      setLoading(false)
    }
  }

  // Debounced loading function
  const debouncedLoadPricingVariants = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    debounceTimeoutRef.current = setTimeout(() => {
      loadPricingVariants()
    }, 500)
  }, [loadPricingVariants])

  const calculateLoan = useCallback(async () => {
    if (!selectedVariant) return

    setCalculationLoading(true)

    try {
      // Generate good type IDs using static fallback mapping
      const principal = calculateProductPrice() // Use actual product price

      // First attempt with the mapped good type ID
      let response = await GraphQLBackend.CalculateBNPLoan({
        cartToken: cookie.get('cartToken') || '',
        downPayment,
        pricingVariantId: parseInt(selectedVariant.id),
      })

      if (response.calculateBNPLoan) {
        setLoanCalculation(response.calculateBNPLoan)
        onCalculationComplete?.(response.calculateBNPLoan)
        return
      }

      onCalculationComplete?.(response.calculateBNPLoan)
    } catch (err) {
      const errorMessage = handleBNPGoodTypeError(err)
    } finally {
      setCalculationLoading(false)
    }
  }, [calculateProductPrice, downPayment, selectedVariant])

  const handleVariantSelect = (variant: BNPPricingVariant) => {
    setSelectedVariant(variant)
    onVariantSelect?.(variant)
  }

  // Handle down payment input change
  const handleDownPaymentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = formatCurrencyInput(e.target.value)
    setDownPaymentInput(inputValue)

    const numericValue = parseCurrencyInput(inputValue)
    const validationError = validateDownPayment(numericValue)

    setDownPaymentError(validationError)

    if (!validationError && numericValue !== downPayment) {
      setDownPayment(numericValue)
      onDownPaymentChange?.(numericValue)
      // Clear current variants and selected variant during recalculation
      setSelectedVariant(null)
      setLoanCalculation(null)
    }
  }

  // Handle down payment input blur (format the display)
  const handleDownPaymentBlur = () => {
    if (downPaymentInput && !downPaymentError) {
      const numericValue = parseCurrencyInput(downPaymentInput)
      setDownPaymentInput(formatDownPaymentInput(numericValue.toString()))
    }
  }

  const calculatePrincipal = (): number => {
    // This should be calculated based on product price and quantity
    // For now, using a mock value
    return 2500.0
  }

  // Load pricing variants on component mount and when dependencies change
  useEffect(() => {
    if (sku) {
      loadPricingVariants()
    }
  }, [sku, quantity])

  // Load pricing variants when down payment changes (debounced)
  useEffect(() => {
    if (sku && downPayment !== initialDownPayment) {
      debouncedLoadPricingVariants()
    }
  }, [sku, downPayment, debouncedLoadPricingVariants])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  // Calculate loan when variant is selected
  useEffect(() => {
    if (selectedVariant) {
      calculateLoan()
    }
  }, [calculateLoan, selectedVariant])

  if (loading) {
    return (
      <div className={`p-6 border border-gray-200 rounded-lg bg-white ${className}`}>
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
          <span className="ml-3 text-gray-600">Зареждане на калкулатора...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`p-6 border border-red-200 rounded-lg bg-red-50 ${className}`}>
        <div className="flex items-center">
          <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
          <div>
            <h4 className="text-red-800 font-medium">Грешка при зареждане</h4>
            <p className="text-red-600 text-sm">{error}</p>
            <button
              type="button"
              onClick={loadPricingVariants}
              className="mt-2 text-sm text-red-700 underline hover:text-red-900"
            >
              Опитайте отново
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Calculator Header */}
      <div className="bg-gradient-to-r from-orange-50 to-orange-50 border border-orange-200 rounded-lg p-6">
        <div className="flex items-center mb-6">
          <svg className="w-8 h-8 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
            />
          </svg>
          <h2 className="text-xl font-bold text-gray-900">BNP Paribas Калкулатор</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div>
            <span className="text-gray-600 text-sm">Продукт:</span>
            <div className="font-semibold text-gray-900">{sku}</div>
          </div>
          <div>
            <span className="text-gray-600 text-sm">Количество:</span>
            <div className="font-semibold text-gray-900">{quantity}</div>
          </div>
          <div>
            <span className="text-gray-600 text-sm">Ориентировъчна цена:</span>
            <div className="font-semibold text-gray-900">{formatPrice(calculateProductPrice())}</div>
          </div>
          <div>
            <label htmlFor="downPayment" className="text-gray-600 text-sm block mb-2">
              Първоначална вноска:
            </label>
            <div className="relative">
              <input
                type="text"
                id="downPayment"
                value={downPaymentInput}
                onChange={handleDownPaymentChange}
                onBlur={handleDownPaymentBlur}
                placeholder="0,00"
                className={`w-full px-3 py-2 border rounded-md text-sm font-semibold focus:outline-none focus:ring-2 transition-colors ${
                  downPaymentError
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500 text-red-600'
                    : 'border-blue-300 focus:ring-blue-500 focus:border-blue-500 text-blue-600'
                }`}
              />
              <span className="absolute right-3 top-2 text-sm text-gray-500">лв.</span>
            </div>
            {downPaymentError && <p className="mt-1 text-xs text-red-600">{downPaymentError}</p>}
            {!downPaymentError && downPayment > 0 && (
              <p className="mt-1 text-xs text-green-600">
                {((downPayment / calculateProductPrice()) * 100).toFixed(1)}% от цената
              </p>
            )}
          </div>
        </div>

        {/* Down Payment Impact */}
        {downPayment > 0 && selectedVariant && (
          <div className="mt-6 p-4 bg-white rounded-lg border border-blue-200">
            <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
              <svg className="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                  clipRule="evenodd"
                />
              </svg>
              Влияние на първоначалната вноска
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="text-gray-600">Финансирана сума:</div>
                <div className="font-bold text-blue-600">{formatPrice(calculateProductPrice() - downPayment)}</div>
              </div>
              <div className="text-center">
                <div className="text-gray-600">Месечна вноска:</div>
                <div className="font-bold text-green-600">{formatPrice(selectedVariant.installmentAmount)}</div>
              </div>
              <div className="text-center">
                <div className="text-gray-600">Общо лихви:</div>
                <div className="font-bold text-gray-900">
                  {formatPrice(
                    parseFloat(selectedVariant.totalRepaymentAmount) - (calculateProductPrice() - downPayment)
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Recalculation indicator */}
        {loading && (
          <div className="mt-4 flex items-center justify-center text-blue-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
            <span className="text-sm">Преизчисляване на варианти...</span>
          </div>
        )}
      </div>

      {/* Variant Selector */}
      <BNPVariantSelector
        variantGroups={variantGroups}
        selectedVariant={selectedVariant}
        onVariantSelectAction={handleVariantSelect}
        loading={calculationLoading}
      />

      {/* Loan Calculation Details */}
      {loanCalculation && <ParibaLoanDetails loanCalculation={loanCalculation} />}
    </div>
  )
}
