// TypeScript interfaces for BNP Paribas payment integration
// Based on GraphQL schema from bnp-queries.txt

export interface BNPPricingScheme {
  id: string
  name: string
}

export interface BNPPricingVariant {
  id: string
  apr: string
  correctDownpaymentAmount: string
  installmentAmount: string
  maturity: string
  nir: string
  pricingSchemeId: string
  pricingSchemeName: string
  totalRepaymentAmount: string
  processingFeeAmount?: string
  isPromo?: boolean
}

export interface BNPCalculatorResponse {
  schemeId: string
  variants: BNPPricingVariant[]
}

export interface BNPVariantGroup {
  schemeId: string
  schemeName: string
  variants: BNPPricingVariant[]
}

export interface BNPLoanCalculation {
  apr: string
  correctDownpaymentAmount: string
  installmentAmount: string
  maturity: string
  nir: string
  pricingSchemeId: string
  pricingSchemeName: string
  pricingVariantId: string
  processingFeeAmount: string
  totalRepaymentAmount: string
}

export interface BNPCustomerData {
  firstName: string
  lastName: string
  phone: string
  email: string
  address: string
  city: string
  postCode: string
  egn?: string
  companyName?: string
  eik?: string
  mol?: string
}

export interface BNPPaymentInput {
  downPayment: number
  pricingVariantId: number
  customerData: BNPCustomerData
}

export interface BNPCartItem {
  id: string
  sku: string
  baseQty: number
  price: {
    amount: number
    currency: string
  }
}

export interface BNPPaymentMethod {
  code: string
  title: string
}

export interface BNPCartTotal {
  code: string
  title: string
  value: {
    amount: number
    currency: string
  }
}

export interface BNPCartSaveResponse {
  id: string
  items: BNPCartItem[]
  paymentMethod: BNPPaymentMethod
  totals: BNPCartTotal[]
}

export interface BNPPaymentProps {
  totalPrice: number
  cartToken: string // Required for saving payment data to cart
  onVariantSelect?: (variantId: string) => void
  onEgnChange?: (egn: string) => void
  onPrivacyChange?: (agreed: boolean) => void
  onError?: (error: string) => void
  className?: string
  // Simplified form integration props
  selectedVariantId?: string
  egn?: string
  privacyAgreed?: boolean
}

export interface BNPCalculatorProps {
  sku: string
  downPayment: number | null
  quantity: number
  selectedScheme: BNPPricingScheme['id'] | null
  onVariantSelect?: (variant: BNPPricingVariant) => void
  onCalculationComplete?: (calculation: BNPLoanCalculation) => void
  onDownPaymentChange?: (downPayment: number) => void
  className?: string
}

export interface BNPDownPaymentProps {
  downPayment: number | null
  onDownPaymentChangeAction: (downPayment: number) => void
  totalPrice: number
  className?: string
}

export interface BNPVariantSelectorProps {
  variantGroups: BNPVariantGroup[]
  selectedVariant: BNPPricingVariant | null
  onVariantSelectAction: (variant: BNPPricingVariant) => void
  loading?: boolean
  loadingVariants?: boolean
  className?: string
}

export interface BNPCustomerFormProps {
  onSubmit: (customerData: BNPCustomerData) => void
  loading?: boolean
  className?: string
  formControl?: any
  initialData?: BNPCustomerData | null
}

// Form validation types
export interface BNPFormErrors {
  firstName?: string
  lastName?: string
  phone?: string
  email?: string
  address?: string
  city?: string
  postCode?: string
  egn?: string
}

// Component state types
export type BNPPaymentStep = 'schemes' | 'variants' | 'customer' | 'confirmation'

export interface BNPPaymentState {
  loading: boolean
  error: string | null
  schemes: BNPPricingScheme[]
  selectedScheme: BNPPricingScheme['id'] | null
  variantGroups: BNPVariantGroup[]
  selectedVariant: BNPPricingVariant | null
  loanCalculation: BNPLoanCalculation | null
  customerData: BNPCustomerData | null
  downPayment: number | null
  goodTypeIds: string
  privacyAgreed: boolean
}

// API response types
export interface BNPApiResponse<T> {
  data: T
  errors?: Array<{
    message: string
    extensions?: any
  }>
}

export interface BNPErrorResponse {
  message: string
  code?: string
  details?: any
}
