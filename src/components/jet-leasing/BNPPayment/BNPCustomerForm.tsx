'use client'
import React, { useState, useEffect } from 'react'
import { useFormContext, useController } from 'react-hook-form'
import { BNPCustomerFormProps, BNPCustomerData, BNPFormErrors } from './types'
import { validateCustomerForm, hasFormErrors } from './utils'
import { useCartStore } from '@features/cart/cart-state'
import { PaymentSchema } from '@features/checkout/components/forms/payment/payment.schema'

export const BNPCustomerForm: React.FC<BNPCustomerFormProps> = ({
  onSubmit,
  loading = false,
  className = '',
  formControl,
  initialData,
}) => {
  const cartStore = useCartStore()
  const form = formControl ? { control: formControl } : useFormContext<PaymentSchema>()

  // Use form controllers for each field when form control is available
  const firstNameController = useController({
    name: 'bnpPaymentData.customerData.firstName',
    control: form.control,
    defaultValue: initialData?.firstName || cartStore.customer.firstName || '',
  })

  const lastNameController = useController({
    name: 'bnpPaymentData.customerData.lastName',
    control: form.control,
    defaultValue: initialData?.lastName || cartStore.customer.lastName || '',
  })

  const emailController = useController({
    name: 'bnpPaymentData.customerData.email',
    control: form.control,
    defaultValue: initialData?.email || cartStore.customer.email || '',
  })

  const phoneController = useController({
    name: 'bnpPaymentData.customerData.phone',
    control: form.control,
    defaultValue: initialData?.phone || cartStore.customer.phone || '',
  })

  const addressController = useController({
    name: 'bnpPaymentData.customerData.address',
    control: form.control,
    defaultValue: initialData?.address || '',
  })

  const cityController = useController({
    name: 'bnpPaymentData.customerData.city',
    control: form.control,
    defaultValue: initialData?.city || '',
  })

  const postCodeController = useController({
    name: 'bnpPaymentData.customerData.postCode',
    control: form.control,
    defaultValue: initialData?.postCode || '',
  })

  const egnController = useController({
    name: 'bnpPaymentData.customerData.egn',
    control: form.control,
    defaultValue: initialData?.egn || '',
  })

  const privacyController = useController({
    name: 'bnpPaymentData.privacyAgreed',
    control: form.control,
    defaultValue: false,
  })

  // Fallback state for when form control is not available
  const [fallbackFormData, setFallbackFormData] = useState<Partial<BNPCustomerData>>({
    firstName: initialData?.firstName || cartStore.customer.firstName || '',
    lastName: initialData?.lastName || cartStore.customer.lastName || '',
    email: initialData?.email || cartStore.customer.email || '',
    phone: initialData?.phone || cartStore.customer.phone || '',
    address: initialData?.address || '',
    city: initialData?.city || '',
    postCode: initialData?.postCode || '',
    egn: initialData?.egn || '',
  })

  const [fallbackPrivacyAgreed, setFallbackPrivacyAgreed] = useState(false)
  const [errors, setErrors] = useState<BNPFormErrors>({})

  const handleFallbackInputChange = (field: keyof BNPCustomerData, value: string) => {
    setFallbackFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleBlur = (field: keyof BNPCustomerData) => {
    // No validation on blur since validation is disabled
  }

  const handleSubmit = (e: React.FormEvent) => {
    console.log('Raw form submit event')
    e.preventDefault()

    // Get current form values
    const currentFormData = formControl ? {
      firstName: firstNameController.field.value,
      lastName: lastNameController.field.value,
      email: emailController.field.value,
      phone: phoneController.field.value,
      address: addressController.field.value,
      city: cityController.field.value,
      postCode: postCodeController.field.value,
      egn: egnController.field.value,
    } : fallbackFormData

    const currentPrivacyAgreed = formControl ? privacyController.field.value : fallbackPrivacyAgreed

    // Check privacy agreement
    if (!currentPrivacyAgreed) {
      alert('Моля, съгласете се с условията за обработка на лични данни.')
      return
    }

    // No validation - directly submit the form
    onSubmit(currentFormData as BNPCustomerData)
  }

  const renderInput = (field: keyof BNPCustomerData, label: string, type: string = 'text', placeholder?: string) => {
    // Get the appropriate controller based on field name
    let controller
    let value
    let onChange

    if (formControl) {
      switch (field) {
        case 'firstName':
          controller = firstNameController
          break
        case 'lastName':
          controller = lastNameController
          break
        case 'email':
          controller = emailController
          break
        case 'phone':
          controller = phoneController
          break
        case 'address':
          controller = addressController
          break
        case 'city':
          controller = cityController
          break
        case 'postCode':
          controller = postCodeController
          break
        case 'egn':
          controller = egnController
          break
      }

      if (controller) {
        value = controller.field.value
        onChange = controller.field.onChange
      }
    } else {
      value = fallbackFormData[field] || ''
      onChange = (e: React.ChangeEvent<HTMLInputElement>) => handleFallbackInputChange(field, e.target.value)
    }

    return (
      <div>
        <label htmlFor={field} className="block text-sm font-medium text-gray-700 mb-1">
          {label} <span className="text-red-500">*</span>
        </label>
        <input
          type={type}
          id={field}
          name={field}
          value={value}
          onChange={onChange}
          onBlur={() => handleBlur(field)}
          placeholder={placeholder}
          disabled={loading}
          className={`w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 transition-colors border-gray-300 focus:ring-orange-500 focus:border-orange-500 ${loading ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`}
        />
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Form */}
      <form className="space-y-6">
        {/* Personal Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h4 className="font-semibold text-gray-900 mb-4">Лични данни</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {renderInput('egn', 'ЕГН', 'text', 'Въведете вашия ЕГН')}
          </div>
        </div>

        {/* Privacy Agreement Checkbox */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <input
              type="checkbox"
              id="privacy-agreement"
              checked={formControl ? privacyController.field.value : fallbackPrivacyAgreed}
              onChange={formControl ? privacyController.field.onChange : (e) => setFallbackPrivacyAgreed(e.target.checked)}
              disabled={loading}
              className="mt-1 mr-3 w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
              required
            />
            <label htmlFor="privacy-agreement" className="text-sm text-blue-800 cursor-pointer">
              <p className="font-medium mb-1">
                Съгласие за обработка на лични данни <span className="text-red-500">*</span>
              </p>
              <p>
                Съгласявам се с{' '}
                <a
                  href="/bnp_pariba"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 underline hover:text-blue-800"
                >
                  условията за обработка на лични данни и условия за кредитиране
                </a>
                . Вашите данни ще бъдат използвани единствено за обработка на заявката за кредит и няма да бъдат
                предоставени на трети страни без ваше съгласие.
              </p>
            </label>
          </div>
        </div>
      </form>
    </div>
  )
}
