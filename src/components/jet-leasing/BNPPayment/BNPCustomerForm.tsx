'use client'
import React, { useState } from 'react'
import { BNPCustomerFormProps, BNPCustomerData, BNPFormErrors } from './types'
import { validateCustomerForm, hasFormErrors } from './utils'
import { useCartStore } from '@features/cart/cart-state'

export const BNPCustomerForm: React.FC<BNPCustomerFormProps> = ({
  onSubmit,
  loading = false,
  className = '',
}) => {
  const cartStore = useCartStore()
  const [formData, setFormData] = useState<Partial<BNPCustomerData>>({
    firstName: cartStore.customer.firstName,
    lastName: cartStore.customer.lastName,
    email: cartStore.customer.email,
    phone: cartStore.customer.phone,
    address: '',
    city: '',
    postCode: '',
    egn: '',
  })

  const [errors, setErrors] = useState<BNPFormErrors>({})
  const [privacyAgreed, setPrivacyAgreed] = useState(false)

  const handleInputChange = (field: keyof BNPCustomerData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleBlur = (field: keyof BNPCustomerData) => {
    // No validation on blur since validation is disabled
  }

  const handleSubmit = (e: React.FormEvent) => {
    console.log('Raw form submit event')
    e.preventDefault()

    // Check privacy agreement
    if (!privacyAgreed) {
      alert('Моля, съгласете се с условията за обработка на лични данни.')
      return
    }

    // No validation - directly submit the form
    onSubmit(formData as BNPCustomerData)
  }

  const renderInput = (field: keyof BNPCustomerData, label: string, type: string = 'text', placeholder?: string) => {
    return (
      <div>
        <label htmlFor={field} className="block text-sm font-medium text-gray-700 mb-1">
          {label} <span className="text-red-500">*</span>
        </label>
        <input
          type={type}
          id={field}
          name={field}
          value={formData[field] || ''}
          onChange={(e) => handleInputChange(field, e.target.value)}
          onBlur={() => handleBlur(field)}
          placeholder={placeholder}
          disabled={loading}
          className={`w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 transition-colors border-gray-300 focus:ring-orange-500 focus:border-orange-500 ${loading ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`}
        />
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Form */}
      <form className="space-y-6">
        {/* Personal Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h4 className="font-semibold text-gray-900 mb-4">Лични данни</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {renderInput('egn', 'ЕГН', 'text', 'Въведете вашия ЕГН')}
          </div>
        </div>

        {/* Privacy Agreement Checkbox */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <input
              type="checkbox"
              id="privacy-agreement"
              checked={privacyAgreed}
              onChange={(e) => setPrivacyAgreed(e.target.checked)}
              disabled={loading}
              className="mt-1 mr-3 w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
              required
            />
            <label htmlFor="privacy-agreement" className="text-sm text-blue-800 cursor-pointer">
              <p className="font-medium mb-1">
                Съгласие за обработка на лични данни <span className="text-red-500">*</span>
              </p>
              <p>
                Съгласявам се с{' '}
                <a
                  href="/bnp_pariba"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 underline hover:text-blue-800"
                >
                  условията за обработка на лични данни и условия за кредитиране
                </a>
                . Вашите данни ще бъдат използвани единствено за обработка на заявката за кредит и няма да бъдат
                предоставени на трети страни без ваше съгласие.
              </p>
            </label>
          </div>
        </div>
      </form>
    </div>
  )
}
