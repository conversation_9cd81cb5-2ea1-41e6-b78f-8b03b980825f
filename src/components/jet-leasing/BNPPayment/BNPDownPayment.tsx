'use client'
import React, { useState } from 'react'
import { BNPPricingVariant, BNPLoanCalculation, BNPVariantGroup, BNPDownPaymentProps } from './types'
import { formatPrice, formatCurrencyInput, parseCurrencyInput } from './utils'

export const BNPDownPayment: React.FC<BNPDownPaymentProps> = ({
  downPayment: initialDownPayment,
  onDownPaymentChangeAction,
  className = '',
  totalPrice,
}) => {
  const [error, setError] = useState<string | null>('')
  const [selectedVariant, setSelectedVariant] = useState<BNPPricingVariant | null>(null)
  const [downPayment, setDownPayment] = useState<number>(initialDownPayment || 0)
  const [downPaymentInput, setDownPaymentInput] = useState<string>(initialDownPayment?.toString() || '')
  const [downPaymentError, setDownPaymentError] = useState<string | null>(null)

  // Validate down payment amount
  const validateDownPayment = (amount: number): string | null => {
    console.log({ amount, totalPrice })
    const minDownPayment = totalPrice * 0.1 // 10% minimum
    const maxDownPayment = totalPrice * 0.8 // 80% maximum

    if (amount < 0) {
      return 'Първоначалната вноска не може да бъде отрицателна'
    }
    if (amount < minDownPayment) {
      return `Минималната първоначална вноска е ${formatPrice(minDownPayment)}`
    }
    if (amount > maxDownPayment) {
      return `Максималната първоначална вноска е ${formatPrice(maxDownPayment)}`
    }
    return null
  }

  // Format down payment input for display
  const formatDownPaymentInput = (value: string): string => {
    const number = parseCurrencyInput(value)
    if (number === 0) return ''
    return number.toFixed(2).replace('.', ',')
  }

  // Handle down payment input change
  const handleDownPaymentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = formatCurrencyInput(e.target.value)
    if (inputValue === '' || /^0+(,0{0,2})?$/.test(inputValue)) {
      setDownPaymentInput('')
      setDownPaymentError(null)
      setDownPayment(0)
      onDownPaymentChangeAction?.(0)
      return
    }

    setDownPaymentInput(inputValue)

    const numericValue = parseCurrencyInput(inputValue)
    const validationError = validateDownPayment(numericValue)

    setDownPaymentError(validationError)

    if (!validationError && numericValue !== downPayment) {
      setDownPayment(numericValue)
      onDownPaymentChangeAction?.(numericValue)
    }
  }

  // Handle down payment input blur (format the display)
  const handleDownPaymentBlur = () => {
    if (downPaymentInput && !downPaymentError) {
      const numericValue = parseCurrencyInput(downPaymentInput)
      setDownPaymentInput(formatDownPaymentInput(numericValue.toString()))
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Calculator Header */}
      <div className="flex flex-row justify-between bg-gradient-to-r from-orange-50 to-orange-50 border border-orange-200 rounded-lg p-6">
        <div className="flex items-center">
          <svg className="w-8 h-8 text-orange-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
            />
          </svg>
          <h2 className="text-sm font-bold text-gray-900">
            <label htmlFor="downPayment">Първоначална вноска:</label>
          </h2>
        </div>

        <div className="gap-6">
          <div>
            <div className="relative">
              <input
                type="text"
                id="downPayment"
                value={downPaymentInput}
                onChange={handleDownPaymentChange}
                onBlur={handleDownPaymentBlur}
                placeholder="0,00"
                className={`w-full px-3 py-2 border rounded-md text-sm font-semibold focus:outline-none focus:ring-2 transition-colors ${
                  downPaymentError
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500 text-red-600'
                    : 'border-orange-300 focus:ring-orange-500 focus:border-orange-500 text-orange-600'
                }`}
              />
              <span className="absolute right-3 top-2 text-sm text-gray-500">лв.</span>
            </div>
            {downPaymentError && <p className="mt-1 text-xs text-red-600">{downPaymentError}</p>}
            {!downPaymentError && downPayment > 0 && (
              <p className="mt-1 text-xs">{((downPayment / totalPrice) * 100).toFixed(1)}% от цената</p>
            )}
          </div>
        </div>

        {/* Down Payment Impact */}
        {downPayment > 0 && selectedVariant && (
          <div className="mt-6 p-4 bg-white rounded-lg border border-blue-200">
            <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
              <svg className="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                  clipRule="evenodd"
                />
              </svg>
              Влияние на първоначалната вноска
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="text-gray-600">Финансирана сума:</div>
                <div className="font-bold text-blue-600">{formatPrice(totalPrice - downPayment)}</div>
              </div>
              <div className="text-center">
                <div className="text-gray-600">Месечна вноска:</div>
                <div className="font-bold text-green-600">{formatPrice(selectedVariant.installmentAmount)}</div>
              </div>
              <div className="text-center">
                <div className="text-gray-600">Общо лихви:</div>
                <div className="font-bold text-gray-900">
                  {formatPrice(parseFloat(selectedVariant.totalRepaymentAmount) - (totalPrice - downPayment))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
