import { z } from 'zod'

export const paymentSchema = z.object({
  paymentMethod: z
    .string()
    .nullable()
    .refine((val) => !!val, {
      message: 'Моля, изберете начин на плащане',
    }),
  comment: z.string().nullable(),
  agreement: z.boolean().refine((val) => val === true, {
    message: 'Трябва да се съгласите с общите условия',
  }),
  adult: z.boolean().refine((val) => val === true, {
    message: 'Трябва да потвърдите, че сте навършили 18 години',
  }),
  subscribe: z.boolean(),
  promoCode: z.string().optional().default(''),
  // Simplified BNP Payment fields - conditional validation like other required fields
  bnpSelectedVariantId: z.string().optional(),
  bnpEgn: z.string().optional(),
  bnpPrivacyAgreed: z.boolean().optional(),
})
.refine((data) => {
  // Conditional validation for BNP variant selection
  if (data.paymentMethod === 'stenik_leasingjetcredit') {
    return data.bnpSelectedVariantId && data.bnpSelectedVariantId.length > 0
  }
  return true
}, {
  message: 'Моля, изберете схема на плащане',
  path: ['bnpSelectedVariantId']
})
.refine((data) => {
  // Conditional validation for BNP EGN field
  if (data.paymentMethod === 'stenik_leasingjetcredit') {
    return data.bnpEgn && data.bnpEgn.length > 0
  }
  return true
}, {
  message: 'Моля, въведете ЕГН',
  path: ['bnpEgn']
})
.refine((data) => {
  // Conditional validation for BNP privacy agreement - same pattern as other required checkboxes
  if (data.paymentMethod === 'stenik_leasingjetcredit') {
    return data.bnpPrivacyAgreed === true
  }
  return true
}, {
  message: 'Трябва да се съгласите с условията за обработка на лични данни за BNP кредитиране',
  path: ['bnpPrivacyAgreed']
})

export type PaymentSchema = z.infer<typeof paymentSchema>
