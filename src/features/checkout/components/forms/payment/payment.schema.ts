import { z } from 'zod'

// BNP Customer Data Schema
const bnpCustomerDataSchema = z.object({
  firstName: z.string().min(1, 'Името е задължително'),
  lastName: z.string().min(1, 'Фамилията е задължителна'),
  phone: z.string().min(1, 'Телефонът е задължителен'),
  email: z.string().min(1, 'Email е задължителен').email('Невалиден email'),
  address: z.string().min(1, 'Адресът е задължителен'),
  city: z.string().min(1, 'Градът е задължителен'),
  postCode: z.string().min(1, 'Пощенският код е задължителен'),
  egn: z.string().optional(),
  companyName: z.string().optional(),
  eik: z.string().optional(),
  mol: z.string().optional(),
})

// BNP Payment Data Schema
const bnpPaymentDataSchema = z.object({
  downPayment: z.number().min(0, 'Първоначалната вноска не може да бъде отрицателна'),
  selectedVariantId: z.string().nullable(),
  customerData: bnpCustomerDataSchema.optional(),
  privacyAgreed: z.boolean().refine((val) => val === true, {
    message: 'Трябва да се съгласите с условията за обработка на лични данни',
  }),
})

export const paymentSchema = z.object({
  paymentMethod: z
    .string()
    .nullable()
    .refine((val) => !!val, {
      message: 'Моля, изберете начин на плащане',
    }),
  comment: z.string().nullable(),
  agreement: z.boolean().refine((val) => val === true, {
    message: 'Трябва да се съгласите с общите условия',
  }),
  adult: z.boolean().refine((val) => val === true, {
    message: 'Трябва да потвърдите, че сте навършили 18 години',
  }),
  subscribe: z.boolean(),
  promoCode: z.string().optional().default(''),
  // BNP Payment fields - only validated when BNP payment method is selected
  bnpPaymentData: bnpPaymentDataSchema.optional(),
}).refine((data) => {
  // Conditional validation for BNP payment method
  if (data.paymentMethod === 'stenik_leasingjetcredit') {
    return data.bnpPaymentData &&
           data.bnpPaymentData.selectedVariantId &&
           data.bnpPaymentData.customerData &&
           data.bnpPaymentData.privacyAgreed
  }
  return true
}, {
  message: 'Моля, попълнете всички задължителни полета за BNP плащане',
  path: ['bnpPaymentData']
})

export type PaymentSchema = z.infer<typeof paymentSchema>
export type BNPCustomerDataSchema = z.infer<typeof bnpCustomerDataSchema>
export type BNPPaymentDataSchema = z.infer<typeof bnpPaymentDataSchema>
