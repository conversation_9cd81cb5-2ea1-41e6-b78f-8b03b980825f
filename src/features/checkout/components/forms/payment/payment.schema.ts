import { z } from 'zod'

export const paymentSchema = z.object({
  paymentMethod: z
    .string()
    .nullable()
    .refine((val) => !!val, {
      message: 'Моля, изберете начин на плащане',
    }),
  comment: z.string().nullable(),
  agreement: z.boolean().refine((val) => val === true, {
    message: 'Трябва да се съгласите с общите условия',
  }),
  adult: z.boolean().refine((val) => val === true, {
    message: 'Трябва да потвърдите, че сте навършили 18 години',
  }),
  subscribe: z.boolean(),
  promoCode: z.string().optional().default(''),
  // Simplified BNP Payment fields - only 2 mandatory fields (privacy handled by main form)
  bnpSelectedVariantId: z.string().optional(),
  bnpEgn: z.string().optional(),
  bnpPrivacyAgreed: z.boolean().optional(),
}).refine((data) => {
  // Conditional validation for BNP payment method - only 2 fields required in BNP section
  if (data.paymentMethod === 'stenik_leasingjetcredit') {
    const hasVariant = data.bnpSelectedVariantId && data.bnpSelectedVariantId.length > 0
    const hasEgn = data.bnpEgn && data.bnpEgn.length > 0
    const hasPrivacyAgreed = data.bnpPrivacyAgreed === true

    if (!hasVariant) {
      return false
    }
    if (!hasEgn) {
      return false
    }
    if (!hasPrivacyAgreed) {
      return false
    }
  }
  return true
}, {
  message: 'Моля, попълнете всички задължителни полета за BNP плащане: изберете схема на плащане, въведете ЕГН и се съгласете с условията за BNP кредитиране',
  path: ['bnpSelectedVariantId']
})

export type PaymentSchema = z.infer<typeof paymentSchema>
