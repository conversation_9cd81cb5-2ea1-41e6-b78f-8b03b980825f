import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { LucideLoaderCircle } from 'lucide-react'
import React, { useEffect, useRef } from 'react'
import { <PERSON><PERSON>rovider, SubmitHandler, useForm, useWatch } from 'react-hook-form'

import Text from '@atoms/Text'
import { Callout } from '@components/molecules/Callout'
import { FormCheckbox } from '@components/molecules/FormControllers/FormCheckbox'
import { FormRadioGroup } from '@components/molecules/FormControllers/FormRadioGroup'
import { FormSubmit } from '@components/molecules/FormControllers/FormSubmit'
import { FormTextArea } from '@components/molecules/FormControllers/FormTextArea'
import { FormProps } from '@components/pages/Checkout/types'
import { CardFooter } from '@components/theme/ui/card'
import { useCartStore } from '@features/cart/cart-state'
import { FormContent } from '@features/checkout/components/forms/components/FormContent'
import { FormGrid } from '@features/checkout/components/forms/components/FormGrid'
import SafePortal from '@features/checkout/components/forms/components/SafePortal'
import { paymentDefaults } from '@features/checkout/components/forms/payment/payment.defaults'
import { PaymentSchema, paymentSchema } from '@features/checkout/components/forms/payment/payment.schema'
import MastercardLogo from '@images/payment-mastercard.inline.svg'
import VisaLogo from '@images/payment-visa.inline.svg'
import { cn } from '@components/lib/utils'
import { BNPPaymentWrapper } from '@components/jet-leasing/BNPPayment/BNPPaymentWrapper'

export const PaymentData = ({ data, isExpanded, formStatus, onSubmitAction }: FormProps<PaymentSchema>) => {
  const [loading, setLoading] = React.useState(false)
  const form = useForm<PaymentSchema>({
    resolver: zodResolver(paymentSchema),
    mode: 'onBlur',
    defaultValues: data || paymentDefaults,
    disabled: loading || formStatus === 'loading',
  })
  const savePaymentMethod = useCartStore((state) => state.savePaymentMethod)
  const paymentMethod = useWatch({ control: form.control, name: 'paymentMethod' })
  const ref = useRef<HTMLFormElement>(null)
  const paymentsList = useCartStore((state) => state.paymentsList)
  const onSubmitHandler: SubmitHandler<PaymentSchema> = (data) => {
    onSubmitAction(data)
  }

  const onErrorHandler = (errors: any) => {
    console.log('Form submission blocked due to errors:', errors)
  }

  const handlePortalButtonClick = () => {
    if (ref && typeof ref === 'object' && ref.current) {
      const formElement = ref.current as HTMLFormElement
      formElement.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }))
    }
  }

  const selectedPayment = paymentsList.data.find((p) => p.code === paymentMethod)

  useEffect(() => {
    if (paymentMethod && paymentMethod !== data?.paymentMethod) {
      setLoading(true)
      savePaymentMethod(paymentMethod).finally(() => setLoading(false))
    }
  }, [savePaymentMethod, paymentMethod, data?.paymentMethod])

  // Clear BNP payment data when switching away from BNP payment method
  useEffect(() => {
    if (paymentMethod !== 'stenik_leasingjetcredit') {
      form.setValue('bnpPaymentData', {
        downPayment: 0,
        selectedVariantId: null,
        customerData: {
          firstName: '',
          lastName: '',
          phone: '',
          email: '',
          address: '',
          city: '',
          postCode: '',
          egn: '',
          companyName: '',
          eik: '',
          mol: '',
        },
        privacyAgreed: false,
      }, { shouldValidate: false })
    }
  }, [paymentMethod, form])

  // Trigger validation when BNP payment method is selected
  useEffect(() => {
    if (paymentMethod === 'stenik_leasingjetcredit') {
      // Trigger validation for BNP fields
      form.trigger('bnpPaymentData')
    }
  }, [paymentMethod, form])

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler, onErrorHandler)} ref={ref as React.Ref<HTMLFormElement>}>
        <FormContent open={isExpanded}>
          <FormGrid>
            {paymentsList.loading && (
              <div className="col-span-2">
                <Callout
                  variant="warning"
                  title="Моля, изчакайте..."
                  icon={<LucideLoaderCircle className="animate-spin" />}
                >
                  <Text>Зареждане на начини за плащане...</Text>
                </Callout>
              </div>
            )}
            {paymentsList.data.length > 0 && (
              <div className="col-span-2">
                <FormRadioGroup
                  validateOnChange
                  name="paymentMethod"
                  options={paymentsList.data
                    // .filter((o) => o.code !== 'extensa_rbb')
                    .map((paymentMethod) => ({
                      value: paymentMethod.code,
                      label: paymentMethod.name,
                      description:
                        paymentMethod.code === 'banktransfer' ? (
                          <div className="flex items-center">
                            <VisaLogo className="w-10 h-5" />
                            <MastercardLogo className="w-10 h-5" />
                          </div>
                        ) : null,
                    }))}
                  className="flef flex-col md:grid md:grid-cols-3 md:gap-y-10 md:gap-x-8"
                  optionClassName="items-start border border-[#D9D9D9] rounded-2xl p-3 gap-3 rounded-xl"
                  optionSelectedClassName="border-[#F1B201] bg-[#F1B201] hover:border-[#F1B201] text-white"
                  radioItemClassName="border-gray-400 w-4 h-4"
                  radioItemSelectedClassName="border-white bg-white"
                  indicatorClassName="bg-[#F1B201] border-none w-2 h-2"
                  labelClassName="gap-2"
                  descriptionSelectedClassName="text-white"
                />
              </div>
            )}
            {selectedPayment?.extraContent && (
              <div className="col-span-2">
                <Callout className="whitespace-pre-line col-span-full" variant="info">
                  {selectedPayment.extraContent}
                </Callout>
              </div>
            )}
            {paymentMethod === 'stenik_leasingjetcredit' && (
              <div className="col-span-2">
                <BNPPaymentWrapper />
                {/* Display BNP validation errors */}
                {form.formState.errors.bnpPaymentData && (
                  <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-600">
                      {form.formState.errors.bnpPaymentData.message}
                    </p>
                  </div>
                )}
              </div>
            )}
            <div className="col-span-2">
              <FormTextArea name="comment" label="Коментар към поръчката" />
            </div>
            <div className="col-span-2">
              <div className="grid grid-cols-1 gap-y-2">
                <FormCheckbox name="agreement" label="Съгласявам се с общите условия" />
                <FormCheckbox name="adult" label="Имам навършени 18 години" />
                <FormCheckbox name="subscribe" label="Запишете ме за бюлетин" />
              </div>
            </div>
          </FormGrid>
        </FormContent>
        <CardFooter className={cn('lg:hidden justify-center mt-4', !isExpanded && 'hidden')}>
          <FormSubmit loading={formStatus === 'loading'} className="tracking-normal">
            <Text className="lg:hidden">Продължете към преглед</Text>
          </FormSubmit>
        </CardFooter>
        {isExpanded && (
          <SafePortal containerSelector="#sidebar-button-container">
            <FormSubmit loading={formStatus === 'loading'} onClick={handlePortalButtonClick}>
              <Text className="lg:hidden tracking-normal sm:tracking-widest">Продължете към преглед</Text>
              <Text className="hidden lg:inline tracking-normal sm:tracking-widest">Завършване на поръчка</Text>
            </FormSubmit>
          </SafePortal>
        )}
      </form>
    </FormProvider>
  )
}

PaymentData.displayName = 'PaymentData'
